import 'dart:io';
import 'package:get/get.dart';

class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final int? statusCode;
  final Map<String, dynamic>? errors;

  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.statusCode,
    this.errors,
  });

  factory ApiResponse.success(T data, {String? message, int? statusCode}) {
    return ApiResponse(
      success: true,
      data: data,
      message: message,
      statusCode: statusCode ?? 200,
    );
  }

  factory ApiResponse.error(
    String message, {
    int? statusCode,
    Map<String, dynamic>? errors,
  }) {
    return ApiResponse(
      success: false,
      message: message,
      statusCode: statusCode ?? 500,
      errors: errors,
    );
  }
}

class ApiService extends GetxService {
  late GetConnect _connect;
  final RxString _authToken = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeConnect();
  }

  void _initializeConnect() {
    _connect = GetConnect(
      timeout: const Duration(seconds: 30),
      allowAutoSignedCert: false,
    );

    // Add request interceptor for authentication
    _connect.httpClient.addRequestModifier<dynamic>((request) {
      if (_authToken.value.isNotEmpty) {
        request.headers['Authorization'] = 'Bearer ${_authToken.value}';
      }
      request.headers['Content-Type'] = 'application/json';
      request.headers['Accept'] = 'application/json';
      return request;
    });

    // Add response interceptor for error handling
    _connect.httpClient.addResponseModifier((request, response) {
      if (response.statusCode == 401) {
        // Handle unauthorized access
        _handleUnauthorized();
      }
      return response;
    });
  }

  void setAuthToken(String token) {
    _authToken.value = token;
  }

  void clearAuthToken() {
    _authToken.value = '';
  }

  void _handleUnauthorized() {
    // Clear token and redirect to login
    clearAuthToken();
    // You can add navigation logic here if needed
    Get.snackbar(
      'خطأ في المصادقة',
      'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى',
    );
  }

  Future<ApiResponse<T>> _handleResponse<T>(
    Future<Response> request,
    T Function(dynamic) fromJson,
  ) async {
    try {
      final response = await request;

      if (response.hasError) {
        return ApiResponse.error(
          response.statusText ?? 'حدث خطأ غير متوقع',
          statusCode: response.statusCode,
        );
      }

      final data = response.body;

      if (data is Map<String, dynamic>) {
        if (data['success'] == true || response.statusCode == 200) {
          return ApiResponse.success(
            fromJson(data['data'] ?? data),
            message: data['message'],
            statusCode: response.statusCode,
          );
        } else {
          return ApiResponse.error(
            data['message'] ?? 'حدث خطأ غير متوقع',
            statusCode: response.statusCode,
            errors: data['errors'],
          );
        }
      }

      return ApiResponse.success(
        fromJson(data),
        statusCode: response.statusCode,
      );
    } on SocketException {
      return ApiResponse.error('خطأ في الاتصال بالشبكة');
    } on FormatException {
      return ApiResponse.error('خطأ في تنسيق البيانات');
    } catch (e) {
      return ApiResponse.error('حدث خطأ غير متوقع: ${e.toString()}');
    }
  }

  // GET request
  Future<ApiResponse<T>> get<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    Map<String, dynamic>? query,
  }) async {
    return _handleResponse(_connect.get(endpoint, query: query), fromJson);
  }

  // POST request
  Future<ApiResponse<T>> post<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    dynamic body,
    Map<String, dynamic>? query,
  }) async {
    return _handleResponse(
      _connect.post(endpoint, body, query: query),
      fromJson,
    );
  }

  // PUT request
  Future<ApiResponse<T>> put<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    dynamic body,
    Map<String, dynamic>? query,
  }) async {
    return _handleResponse(
      _connect.put(endpoint, body, query: query),
      fromJson,
    );
  }

  // DELETE request
  Future<ApiResponse<T>> delete<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    Map<String, dynamic>? query,
  }) async {
    return _handleResponse(_connect.delete(endpoint, query: query), fromJson);
  }

  // PATCH request
  Future<ApiResponse<T>> patch<T>(
    String endpoint,
    T Function(dynamic) fromJson, {
    dynamic body,
    Map<String, dynamic>? query,
  }) async {
    return _handleResponse(
      _connect.patch(endpoint, body, query: query),
      fromJson,
    );
  }

  // Upload file
  Future<ApiResponse<T>> uploadFile<T>(
    String endpoint,
    String filePath,
    T Function(dynamic) fromJson, {
    String fieldName = 'file',
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final form = FormData({
        fieldName: MultipartFile(
          File(filePath),
          filename: filePath.split('/').last,
        ),
        if (additionalData != null) ...additionalData,
      });

      return _handleResponse(_connect.post(endpoint, form), fromJson);
    } catch (e) {
      return ApiResponse.error('فشل في رفع الملف: ${e.toString()}');
    }
  }

  // Download file
  Future<ApiResponse<String>> downloadFile(
    String endpoint,
    String savePath,
  ) async {
    try {
      final response = await _connect.get(endpoint);

      if (response.hasError) {
        return ApiResponse.error(
          response.statusText ?? 'فشل في تحميل الملف',
          statusCode: response.statusCode,
        );
      }

      final file = File(savePath);
      await file.writeAsBytes(response.body);

      return ApiResponse.success(savePath, message: 'تم تحميل الملف بنجاح');
    } catch (e) {
      return ApiResponse.error('فشل في تحميل الملف: ${e.toString()}');
    }
  }
}
