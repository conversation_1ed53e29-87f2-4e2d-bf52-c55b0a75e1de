import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:myrunway/core/models/user_model.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/core/constants/api_endpoints.dart';
import 'package:myrunway/core/services/api_service.dart';

class AuthService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();
  
  final Rx<UserModel?> _currentUser = Rx<UserModel?>(null);
  final RxBool _isLoggedIn = false.obs;
  final RxBool _isLoading = false.obs;

  // Getters
  UserModel? get currentUser => _currentUser.value;
  bool get isLoggedIn => _isLoggedIn.value;
  bool get isLoading => _isLoading.value;
  UserRole? get currentUserRole => _currentUser.value?.role;

  @override
  void onInit() {
    super.onInit();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    // Check if user is already logged in (e.g., from stored token)
    // This is where you would check SharedPreferences or secure storage
    // For now, we'll just set it to false
    _isLoggedIn.value = false;
  }

  Future<bool> login(String email, String password) async {
    _isLoading.value = true;
    
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        ApiEndpoints.login,
        (data) => data as Map<String, dynamic>,
        body: {
          'email': email,
          'password': password,
        },
      );

      if (response.success && response.data != null) {
        final userData = response.data!;
        
        // Set auth token
        if (userData['token'] != null) {
          _apiService.setAuthToken(userData['token']);
        }
        
        // Create user model
        _currentUser.value = UserModel.fromJson(userData['user'] ?? userData);
        _isLoggedIn.value = true;
        
        // Store token securely (implement with SharedPreferences or secure storage)
        // await _storeAuthToken(userData['token']);
        
        return true;
      } else {
        Get.snackbar(
          'خطأ في تسجيل الدخول',
          response.message ?? 'بيانات الدخول غير صحيحة',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تسجيل الدخول',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> loginWithMockData(String email, String password) async {
    _isLoading.value = true;
    
    // Simulate API delay
    await Future.delayed(const Duration(seconds: 1));
    
    try {
      // Mock authentication logic
      UserModel? mockUser = _getMockUser(email, password);
      
      if (mockUser != null) {
        _currentUser.value = mockUser;
        _isLoggedIn.value = true;
        
        Get.snackbar(
          'نجح تسجيل الدخول',
          'مرحباً ${mockUser.name}',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        
        return true;
      } else {
        Get.snackbar(
          'خطأ في تسجيل الدخول',
          'بيانات الدخول غير صحيحة',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } finally {
      _isLoading.value = false;
    }
  }

  UserModel? _getMockUser(String email, String password) {
    // Mock users for testing
    final mockUsers = {
      '<EMAIL>': UserModel(
        id: '1',
        name: 'أحمد محمد',
        email: '<EMAIL>',
        phone: '+966501234567',
        role: UserRole.employee,
        officeId: 'office_1',
        officeName: 'مكتب الرياض الرئيسي',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        lastLoginAt: DateTime.now(),
      ),
      '<EMAIL>': UserModel(
        id: '2',
        name: 'سارة أحمد',
        email: '<EMAIL>',
        phone: '+966501234568',
        role: UserRole.manager,
        officeId: 'office_1',
        officeName: 'مكتب الرياض الرئيسي',
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        lastLoginAt: DateTime.now(),
      ),
      '<EMAIL>': UserModel(
        id: '3',
        name: 'محمد علي',
        email: '<EMAIL>',
        phone: '+966501234569',
        role: UserRole.master,
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
        lastLoginAt: DateTime.now(),
      ),
    };

    // Simple password check (in real app, this would be handled by backend)
    if (password == '123456' && mockUsers.containsKey(email)) {
      return mockUsers[email];
    }
    
    return null;
  }

  Future<void> logout() async {
    _isLoading.value = true;
    
    try {
      // Call logout API if needed
      await _apiService.post<Map<String, dynamic>>(
        ApiEndpoints.logout,
        (data) => data as Map<String, dynamic>,
      );
    } catch (e) {
      // Handle logout error if needed
    }
    
    // Clear local data
    _currentUser.value = null;
    _isLoggedIn.value = false;
    _apiService.clearAuthToken();
    
    // Clear stored token
    // await _clearStoredAuthToken();
    
    _isLoading.value = false;
    
    Get.snackbar(
      'تم تسجيل الخروج',
      'تم تسجيل الخروج بنجاح',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }

  Widget getHomePageForCurrentUser() {
    if (_currentUser.value == null) {
      // Return login page or splash screen
      return Container(); // This will be replaced with actual login page
    }

    switch (_currentUser.value!.role) {
      case UserRole.employee:
        // Return employee home page
        return Container(); // This will be replaced with actual employee home page
      case UserRole.manager:
        // Return manager home page
        return Container(); // This will be replaced with actual manager home page
      case UserRole.master:
        // Return master home page
        return Container(); // This will be replaced with actual master home page
    }
  }

  bool hasPermission(UserRole requiredRole) {
    if (_currentUser.value == null) return false;
    return _currentUser.value!.canAccess(requiredRole);
  }

  bool canAccessEmployeeManagement() {
    return hasPermission(UserRole.manager);
  }

  bool canAccessOrderManagement() {
    return hasPermission(UserRole.manager);
  }

  bool canAccessClientManagement() {
    return hasPermission(UserRole.manager);
  }

  bool canAccessOfficeManagement() {
    return hasPermission(UserRole.master);
  }

  bool canAccessReports() {
    return hasPermission(UserRole.manager);
  }

  bool canAccessAdvancedReports() {
    return hasPermission(UserRole.master);
  }

  bool canAccessSystemSettings() {
    return hasPermission(UserRole.master);
  }

  Future<bool> updateProfile(Map<String, dynamic> profileData) async {
    _isLoading.value = true;
    
    try {
      final response = await _apiService.put<Map<String, dynamic>>(
        ApiEndpoints.updateProfile,
        (data) => data as Map<String, dynamic>,
        body: profileData,
      );

      if (response.success && response.data != null) {
        _currentUser.value = UserModel.fromJson(response.data!);
        Get.snackbar(
          'تم التحديث',
          'تم تحديث الملف الشخصي بنجاح',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        return true;
      } else {
        Get.snackbar(
          'خطأ',
          response.message ?? 'فشل في تحديث الملف الشخصي',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث الملف الشخصي',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> changePassword(String currentPassword, String newPassword) async {
    _isLoading.value = true;
    
    try {
      final response = await _apiService.post<Map<String, dynamic>>(
        ApiEndpoints.changePassword,
        (data) => data as Map<String, dynamic>,
        body: {
          'current_password': currentPassword,
          'new_password': newPassword,
        },
      );

      if (response.success) {
        Get.snackbar(
          'تم التحديث',
          'تم تغيير كلمة المرور بنجاح',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        return true;
      } else {
        Get.snackbar(
          'خطأ',
          response.message ?? 'فشل في تغيير كلمة المرور',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تغيير كلمة المرور',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
}
