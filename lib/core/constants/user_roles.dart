enum UserRole {
  employee,
  manager,
  master,
}

extension UserRoleExtension on UserRole {
  String get name {
    switch (this) {
      case UserRole.employee:
        return 'employee';
      case UserRole.manager:
        return 'manager';
      case UserRole.master:
        return 'master';
    }
  }

  String get displayName {
    switch (this) {
      case UserRole.employee:
        return 'موظف';
      case UserRole.manager:
        return 'مدير';
      case UserRole.master:
        return 'مدير عام';
    }
  }

  int get level {
    switch (this) {
      case UserRole.employee:
        return 1;
      case UserRole.manager:
        return 2;
      case UserRole.master:
        return 3;
    }
  }

  bool get canManageEmployees {
    return this == UserRole.manager || this == UserRole.master;
  }

  bool get canManageOrders {
    return this == UserRole.manager || this == UserRole.master;
  }

  bool get canManageClients {
    return this == UserRole.manager || this == UserRole.master;
  }

  bool get canManageOffices {
    return this == UserRole.master;
  }

  bool get canAccessReports {
    return this == UserRole.manager || this == UserRole.master;
  }

  bool get canAccessAdvancedReports {
    return this == UserRole.master;
  }

  bool get canManageSystemSettings {
    return this == UserRole.master;
  }

  bool hasPermission(UserRole requiredRole) {
    return level >= requiredRole.level;
  }

  static UserRole fromString(String role) {
    switch (role.toLowerCase()) {
      case 'employee':
        return UserRole.employee;
      case 'manager':
        return UserRole.manager;
      case 'master':
        return UserRole.master;
      default:
        return UserRole.employee; // Default to employee if unknown
    }
  }
}

class UserPermissions {
  static bool canAccessEmployeeManagement(UserRole role) {
    return role.canManageEmployees;
  }

  static bool canAccessOrderManagement(UserRole role) {
    return role.canManageOrders;
  }

  static bool canAccessClientManagement(UserRole role) {
    return role.canManageClients;
  }

  static bool canAccessOfficeManagement(UserRole role) {
    return role.canManageOffices;
  }

  static bool canAccessReports(UserRole role) {
    return role.canAccessReports;
  }

  static bool canAccessAdvancedReports(UserRole role) {
    return role.canAccessAdvancedReports;
  }

  static bool canAccessSystemSettings(UserRole role) {
    return role.canManageSystemSettings;
  }

  static bool canAssignOrders(UserRole role) {
    return role.canManageOrders;
  }

  static bool canUpdateOrderStatus(UserRole role) {
    // All roles can update order status, but with different permissions
    return true;
  }

  static bool canDeleteOrders(UserRole role) {
    return role.canManageOrders;
  }

  static bool canCreateEmployees(UserRole role) {
    return role.canManageEmployees;
  }

  static bool canDeleteEmployees(UserRole role) {
    return role.canManageEmployees;
  }

  static bool canViewAllOrders(UserRole role) {
    return role.canManageOrders;
  }

  static bool canViewOwnOrdersOnly(UserRole role) {
    return role == UserRole.employee;
  }
}
