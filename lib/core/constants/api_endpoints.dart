class ApiEndpoints {
  // Base URL - Update this with your actual API base URL
  static const String baseUrl = 'https://api.myrunway.com';
  static const String apiVersion = '/v1';
  static const String apiBase = '$baseUrl$apiVersion';
  
  // Authentication Endpoints
  static const String login = '$apiBase/auth/login';
  static const String logout = '$apiBase/auth/logout';
  static const String register = '$apiBase/auth/register';
  static const String refreshToken = '$apiBase/auth/refresh';
  static const String forgotPassword = '$apiBase/auth/forgot-password';
  static const String resetPassword = '$apiBase/auth/reset-password';
  
  // User Endpoints
  static const String profile = '$apiBase/user/profile';
  static const String updateProfile = '$apiBase/user/profile';
  static const String changePassword = '$apiBase/user/change-password';
  
  // Orders Endpoints
  static const String orders = '$apiBase/orders';
  static const String createOrder = '$apiBase/orders';
  static const String updateOrder = '$apiBase/orders'; // + /{id}
  static const String deleteOrder = '$apiBase/orders'; // + /{id}
  static const String assignOrder = '$apiBase/orders'; // + /{id}/assign
  static const String updateOrderStatus = '$apiBase/orders'; // + /{id}/status
  static const String orderProofOfDelivery = '$apiBase/orders'; // + /{id}/proof
  
  // Employees Endpoints
  static const String employees = '$apiBase/employees';
  static const String createEmployee = '$apiBase/employees';
  static const String updateEmployee = '$apiBase/employees'; // + /{id}
  static const String deleteEmployee = '$apiBase/employees'; // + /{id}
  static const String employeeOrders = '$apiBase/employees'; // + /{id}/orders
  static const String employeePerformance = '$apiBase/employees'; // + /{id}/performance
  
  // Clients Endpoints
  static const String clients = '$apiBase/clients';
  static const String createClient = '$apiBase/clients';
  static const String updateClient = '$apiBase/clients'; // + /{id}
  static const String deleteClient = '$apiBase/clients'; // + /{id}
  static const String clientOrders = '$apiBase/clients'; // + /{id}/orders
  
  // Offices Endpoints
  static const String offices = '$apiBase/offices';
  static const String createOffice = '$apiBase/offices';
  static const String updateOffice = '$apiBase/offices'; // + /{id}
  static const String deleteOffice = '$apiBase/offices'; // + /{id}
  static const String officeEmployees = '$apiBase/offices'; // + /{id}/employees
  static const String officeOrders = '$apiBase/offices'; // + /{id}/orders
  
  // Reports Endpoints
  static const String reportsRevenue = '$apiBase/reports/revenue';
  static const String reportsCommission = '$apiBase/reports/commission';
  static const String reportsDeliveryMetrics = '$apiBase/reports/delivery-metrics';
  static const String reportsEmployeePerformance = '$apiBase/reports/employee-performance';
  static const String reportsOverallMetrics = '$apiBase/reports/overall-metrics';
  
  // Dashboard Endpoints
  static const String dashboardEmployee = '$apiBase/dashboard/employee';
  static const String dashboardManager = '$apiBase/dashboard/manager';
  static const String dashboardMaster = '$apiBase/dashboard/master';
  
  // File Upload Endpoints
  static const String uploadImage = '$apiBase/upload/image';
  static const String uploadDocument = '$apiBase/upload/document';
  
  // Helper methods for dynamic endpoints
  static String orderById(String id) => '$orders/$id';
  static String employeeById(String id) => '$employees/$id';
  static String clientById(String id) => '$clients/$id';
  static String officeById(String id) => '$offices/$id';
  
  static String assignOrderToEmployee(String orderId) => '$orders/$orderId/assign';
  static String updateOrderStatusById(String orderId) => '$orders/$orderId/status';
  static String orderProofById(String orderId) => '$orders/$orderId/proof';
  
  static String employeeOrdersById(String employeeId) => '$employees/$employeeId/orders';
  static String employeePerformanceById(String employeeId) => '$employees/$employeeId/performance';
  
  static String clientOrdersById(String clientId) => '$clients/$clientId/orders';
  
  static String officeEmployeesById(String officeId) => '$offices/$officeId/employees';
  static String officeOrdersById(String officeId) => '$offices/$officeId/orders';
}
