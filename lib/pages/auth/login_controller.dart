import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:myrunway/core/services/auth_service.dart';
import 'package:myrunway/core/constants/user_roles.dart';
import 'package:myrunway/pages/employee/home_page.dart';
import 'package:myrunway/pages/manager/home_page.dart';
import 'package:myrunway/pages/master/home_page.dart';

class LoginController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();

  // Reactive variables
  final RxBool isLoading = false.obs;
  final RxString email = ''.obs;
  final RxString password = ''.obs;
  final RxBool obscurePassword = true.obs;

  void togglePasswordVisibility() {
    obscurePassword.value = !obscurePassword.value;
  }

  Future<void> login() async {
    if (!_validateInputs()) return;

    isLoading.value = true;

    try {
      // Use mock login for now - replace with real API call later
      final success = await _authService.loginWithMockData(
        email.value.trim(),
        password.value,
      );

      if (success) {
        // Navigate to appropriate home page based on user role
        _navigateToHomePage();
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تسجيل الدخول: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  bool _validateInputs() {
    if (email.value.trim().isEmpty) {
      Get.snackbar(
        'خطأ في البيانات',
        'يرجى إدخال البريد الإلكتروني',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return false;
    }

    if (!GetUtils.isEmail(email.value.trim())) {
      Get.snackbar(
        'خطأ في البيانات',
        'يرجى إدخال بريد إلكتروني صحيح',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return false;
    }

    if (password.value.isEmpty) {
      Get.snackbar(
        'خطأ في البيانات',
        'يرجى إدخال كلمة المرور',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return false;
    }

    if (password.value.length < 6) {
      Get.snackbar(
        'خطأ في البيانات',
        'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return false;
    }

    return true;
  }

  void _navigateToHomePage() {
    final userRole = _authService.currentUserRole;
    
    if (userRole == null) {
      Get.snackbar(
        'خطأ',
        'لم يتم تحديد دور المستخدم',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    Widget homePage;
    
    switch (userRole) {
      case UserRole.employee:
        homePage = const EmployeeHomePage();
        break;
      case UserRole.manager:
        homePage = const ManagerHomePage();
        break;
      case UserRole.master:
        homePage = const MasterHomePage();
        break;
    }

    // Use Get.offAll to replace the entire navigation stack
    Get.offAll(() => homePage);
  }

  void forgotPassword() {
    // Navigate to forgot password page or show dialog
    Get.dialog(
      AlertDialog(
        title: const Text('نسيت كلمة المرور؟'),
        content: const Text(
          'يرجى التواصل مع المدير لإعادة تعيين كلمة المرور.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  // Quick login methods for testing
  void quickLoginAsEmployee() {
    email.value = '<EMAIL>';
    password.value = '123456';
    login();
  }

  void quickLoginAsManager() {
    email.value = '<EMAIL>';
    password.value = '123456';
    login();
  }

  void quickLoginAsMaster() {
    email.value = '<EMAIL>';
    password.value = '123456';
    login();
  }

  @override
  void onClose() {
    // Clear sensitive data when controller is disposed
    email.value = '';
    password.value = '';
    super.onClose();
  }
}
